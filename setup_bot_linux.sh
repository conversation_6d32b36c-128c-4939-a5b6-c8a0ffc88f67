#!/bin/bash

# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Node.js and npm
sudo apt install -y curl
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Verify installation
node -v
npm -v

# Install TypeScript and ts-node
sudo npm install -g typescript ts-node

# Create project directory
mkdir -p ~/solana-bot
cd ~/solana-bot

# Create package.json
cat > package.json << 'EOF'
{
  "name": "solana-meme-bot",
  "version": "1.0.0",
  "description": "Fast Solana token sniping bot",
  "main": "solana_meme_bot.ts",
  "scripts": {
    "start": "ts-node solana_meme_bot.ts",
    "start:pm2": "pm2 start ecosystem.config.js"
  },
  "dependencies": {
    "@jup-ag/core": "^4.0.0",
    "@solana/spl-token": "^0.3.8",
    "@solana/web3.js": "^1.78.0",
    "bs58": "^5.0.0",
    "jsbi": "^4.3.0",
    "ws": "^8.14.2"
  }
}
EOF

# Install dependencies
npm install

# Create PM2 ecosystem file for keeping the bot running
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: "solana-meme-bot",
    script: "./solana_meme_bot.ts",
    interpreter: "ts-node",
    watch: false,
    max_memory_restart: "1G",
    env: {
      NODE_ENV: "production",
    },
    log_date_format: "YYYY-MM-DD HH:mm:ss Z",
    restart_delay: 10000
  }]
};
EOF

# Install PM2 for keeping the bot running
sudo npm install -g pm2

# Create the bot file
cat > solana_meme_bot.ts << 'EOF'
import { Connection, PublicKey, Keypair, Transaction, ComputeBudgetProgram } from '@solana/web3.js';
import { TOKEN_PROGRAM_ID } from '@solana/spl-token';
import { Jupiter } from '@jup-ag/core';
import JSBI from 'jsbi';
import bs58 from 'bs58';
import WebSocket from 'ws';

// Configuration
const WATCH_THRESHOLD_MCAP = 45000; // $45k market cap - BUY
const SELL_THRESHOLD_MCAP = 60000;  // $60k market cap - SELL
const USDC_MINT = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');
const BUY_AMOUNT_USDC = 50; // Amount in USDC to buy with

// Multiple fast RPC endpoints for redundancy and speed
const RPC_ENDPOINTS = [
  'https://solana-mainnet.rpc.extrnode.com',
  'https://api.mainnet-beta.solana.com',
  'https://solana-api.projectserum.com'
];

// WebSocket endpoint for real-time updates
const WS_ENDPOINT = 'wss://api.mainnet-beta.solana.com';

// Wallet configuration
const WALLET_TO_TRACK = new PublicKey('5qWya6UjwWnGVhdSBL3hyZ7B45jbk6Byt1hwd7ohEGXE');
const MY_WALLET = new PublicKey('6An8ZM33GPvuZFAs1BP9z5L5pczhPP8YdefDtaLLmUE');
const PRIVATE_KEY = bs58.decode("3WnGTqz9jbYiZyHBpoUPU8mpwwgdWKEB4UW4YvAxAvmWUDpuL1BgYGwf49TCEaezmquP3ah8XWohXZJyQSgUWWeN");

// Priority fee for faster transaction processing
const PRIORITY_FEE_MICROLAMPORTS = 1_000_000; // 0.001 SOL

// Connections to multiple RPCs for redundancy
let connections: Connection[] = [];
let activeConnectionIndex = 0;

// Get a connection, cycling through available endpoints if one fails
function getConnection(): Connection {
  return connections[activeConnectionIndex];
}

// Switch to next connection if current one fails
function rotateConnection() {
  activeConnectionIndex = (activeConnectionIndex + 1) % connections.length;
  console.log(`Switched to RPC endpoint: ${RPC_ENDPOINTS[activeConnectionIndex]}`);
  return getConnection();
}

async function main() {
  // Initialize connections to all RPCs
  connections = RPC_ENDPOINTS.map(endpoint => 
    new Connection(endpoint, {
      commitment: 'confirmed',
      confirmTransactionInitialTimeout: 60000,
      disableRetryOnRateLimit: false,
    })
  );
  
  const wallet = Keypair.fromSecretKey(PRIVATE_KEY);
  console.log(`Using wallet: ${wallet.publicKey.toString()}`);
  console.log(`Tracking wallet: ${WALLET_TO_TRACK.toString()}`);
  
  // Initialize Jupiter for swaps
  const jupiter = await Jupiter.load({
    connection: getConnection(),
    cluster: 'mainnet-beta',
    user: wallet,
  });
  
  // Track tokens we're already monitoring to avoid duplicates
  const monitoringTokens = new Set<string>();
  
  // Track tokens we've already bought
  const boughtTokens = new Set<string>();
  
  // Track tokens we've already sold
  const soldTokens = new Set<string>();
  
  async function monitorToken(tokenAddress: string) {
    if (monitoringTokens.has(tokenAddress)) return;
    monitoringTokens.add(tokenAddress);
    
    const tokenPublicKey = new PublicKey(tokenAddress);
    console.log(`Started monitoring token: ${tokenAddress}`);
    
    let lastMarketCap = 0;
    let consecutiveErrors = 0;
    
    while (true) {
      try {
        const marketCap = await calculateMarketCap(tokenPublicKey);
        
        // Skip if we couldn't calculate market cap
        if (marketCap <= 0) {
          await new Promise(resolve => setTimeout(resolve, 5000));
          continue;
        }
        
        console.log(`[${new Date().toISOString()}] ${tokenAddress} - Market cap: $${marketCap.toFixed(2)}`);
        lastMarketCap = marketCap;
        consecutiveErrors = 0;
        
        // BUY logic
        if (marketCap >= WATCH_THRESHOLD_MCAP && marketCap < SELL_THRESHOLD_MCAP && !boughtTokens.has(tokenAddress)) {
          console.log(`Token reached buy threshold of $${WATCH_THRESHOLD_MCAP}`);
          const success = await buyToken(tokenPublicKey, jupiter);
          if (success) {
            boughtTokens.add(tokenAddress);
          }
        } 
        // SELL logic
        else if (marketCap >= SELL_THRESHOLD_MCAP && boughtTokens.has(tokenAddress) && !soldTokens.has(tokenAddress)) {
          console.log(`Token reached sell threshold of $${SELL_THRESHOLD_MCAP}`);
          const success = await sellToken(tokenPublicKey, jupiter);
          if (success) {
            soldTokens.add(tokenAddress);
            break; // Stop monitoring this token
          }
        }
        
        await new Promise(resolve => setTimeout(resolve, 5000)); // 5 seconds
      } catch (error) {
        console.error(`Error monitoring token ${tokenAddress}:`, error);
        consecutiveErrors++;
        
        if (consecutiveErrors >= 3) {
          rotateConnection(); // Switch RPC if we get multiple errors
          consecutiveErrors = 0;
        }
        
        await new Promise(resolve => setTimeout(resolve, 10000)); // 10 seconds on error
      }
    }
  }
  
  async function calculateMarketCap(tokenPublicKey: PublicKey): Promise<number> {
    try {
      // Get token supply
      const tokenSupply = await getConnection().getTokenSupply(tokenPublicKey);
      const totalSupply = Number(tokenSupply.value.amount) / Math.pow(10, tokenSupply.value.decimals);
      
      // Get token price
      const tokenPrice = await getTokenPriceFromLiquidityPool(tokenPublicKey);
      
      return tokenPrice * totalSupply;
    } catch (error) {
      console.error(`Error calculating market cap for ${tokenPublicKey.toString()}:`, error);
      rotateConnection();
      return 0;
    }
  }
  
  async function getTokenPriceFromLiquidityPool(tokenPublicKey: PublicKey): Promise<number> {
    try {
      // Use Jupiter's route to determine price
      const routes = await jupiter.computeRoutes({
        inputMint: tokenPublicKey,
        outputMint: USDC_MINT,
        amount: JSBI.BigInt(1 * Math.pow(10, 6)), // 1 token with 6 decimals
        slippage: 1,
        forceFetch: true, // Force fresh data
      });
      
      if (routes.routesInfos.length > 0) {
        const bestRoute = routes.routesInfos[0];
        // This is the price of 1 token in USDC
        return Number(bestRoute.outAmount) / Math.pow(10, 6);
      }
      
      return 0;
    } catch (error) {
      console.error("Error getting token price:", error);
      return 0;
    }
  }
  
  async function buyToken(tokenPublicKey: PublicKey, jupiter: Jupiter): Promise<boolean> {
    try {
      console.log(`Buying token ${tokenPublicKey.toString()}`);
      
      // Amount of USDC to use for buying (e.g., $50 worth)
      const inputAmount = JSBI.BigInt(BUY_AMOUNT_USDC * 1_000_000); // USDC has 6 decimals
      
      // Find routes from USDC to the token
      const routes = await jupiter.computeRoutes({
        inputMint: USDC_MINT,
        outputMint: tokenPublicKey,
        amount: inputAmount,
        slippage: 2.5, // 2.5% slippage for faster execution
        forceFetch: true,
      });
      
      if (routes.routesInfos.length === 0) {
        console.error("No routes found for buy");
        return false;
      }
      
      const bestRoute = routes.routesInfos[0];
      console.log(`Expected input: ${BUY_AMOUNT_USDC} USDC`);
      console.log(`Expected output: ${Number(bestRoute.outAmount) / Math.pow(10, 6)} tokens`);
      console.log(`Route: ${bestRoute.marketInfos.map(m => m.label).join(' -> ')}`);
      
      // Add priority fee for faster processing
      const modifyComputeUnits = ComputeBudgetProgram.setComputeUnitPrice({
        microLamports: PRIORITY_FEE_MICROLAMPORTS,
      });
      
      // Execute the swap with priority fee
      const { execute } = await jupiter.exchange({
        routeInfo: bestRoute,
        computeUnitPriceMicroLamports: PRIORITY_FEE_MICROLAMPORTS,
      });
      
      const result = await execute();
      
      if ('txid' in result) {
        console.log(`Buy successful! Txn: https://solscan.io/tx/${result.txid}`);
        return true;
      } else {
        console.error("Buy failed:", result.error);
        return false;
      }
    } catch (error) {
      console.error("Error buying token:", error);
      return false;
    }
  }
  
  async function sellToken(tokenPublicKey: PublicKey, jupiter: Jupiter): Promise<boolean> {
    try {
      console.log(`Selling token ${tokenPublicKey.toString()}`);
      
      // Get token balance
      const tokenAccounts = await getConnection().getParsedTokenAccountsByOwner(
        wallet.publicKey,
        { mint: tokenPublicKey }
      );
      
      if (tokenAccounts.value.length === 0) {
        console.error("No token account found");
        return false;
      }
      
      const tokenAccount = tokenAccounts.value[0];
      const balance = tokenAccount.account.data.parsed.info.tokenAmount.amount;
      const decimals = tokenAccount.account.data.parsed.info.tokenAmount.decimals;
      
      console.log(`Found ${Number(balance) / Math.pow(10, decimals)} tokens to sell`);
      
      // Sell 100% of tokens
      const routes = await jupiter.computeRoutes({
        inputMint: tokenPublicKey,
        outputMint: USDC_MINT,
        amount: JSBI.BigInt(balance),
        slippage: 2.5, // 2.5% slippage for faster execution
        forceFetch: true,
      });
      
      if (routes.routesInfos.length === 0) {
        console.error("No routes found for swap");
        return false;
      }
      
      const bestRoute = routes.routesInfos[0];
      console.log(`Expected output: ${Number(bestRoute.outAmount) / Math.pow(10, 6)} USDC`);
      console.log(`Route: ${bestRoute.marketInfos.map(m => m.label).join(' -> ')}`);
      
      // Add priority fee for faster processing
      const modifyComputeUnits = ComputeBudgetProgram.setComputeUnitPrice({
        microLamports: PRIORITY_FEE_MICROLAMPORTS,
      });
      
      // Execute the swap with priority fee
      const { execute } = await jupiter.exchange({
        routeInfo: bestRoute,
        computeUnitPriceMicroLamports: PRIORITY_FEE_MICROLAMPORTS,
      });
      
      const result = await execute();
      
      if ('txid' in result) {
        console.log(`Sell successful! Txn: https://solscan.io/tx/${result.txid}`);
        return true;
      } else {
        console.error("Sell failed:", result.error);
        return false;
      }
    } catch (error) {
      console.error("Error selling token:", error);
      return false;
    }
  }
  
  // Function to continuously monitor for new tokens using WebSocket
  async function watchForNewTokensRealtime() {
    // Initial token scan
    const initialTokens = await getWalletTokens(WALLET_TO_TRACK.toString());
    console.log(`Initial scan: Found ${initialTokens.length} tokens in tracked wallet`);
    
    // Start monitoring existing tokens
    for (const token of initialTokens) {
      monitorToken(token);
    }
    
    // Set up WebSocket connection for real-time updates
    setupWebSocketMonitoring();
    
    // Also poll periodically as a backup
    pollForNewTokens();
  }
  
  function setupWebSocketMonitoring() {
    const ws = new WebSocket(WS_ENDPOINT);
    
    ws.on('open', () => {
      console.log('WebSocket connected for real-time monitoring');
      
      // Subscribe to account updates for the tracked wallet
      const subscribeMsg = {
        jsonrpc: '2.0',
        id: 1,
        method: 'accountSubscribe',
        params: [
          WALLET_TO_TRACK.toString(),
          {
            encoding: 'jsonParsed',
            commitment: 'confirmed'
          }
        ]
      };
      
      ws.send(JSON.stringify(subscribeMsg));
    });
    
    ws.on('message', async (data) => {
      try {
        const response = JSON.parse(data.toString());
        
        // If we get a notification about the account changing
        if (response.method === 'accountNotification') {
          console.log('Detected wallet activity, checking for new tokens...');
          const currentTokens = await getWalletTokens(WALLET_TO_TRACK.toString());
          
          // Check for new tokens
          for (const token of currentTokens) {
            if (!monitoringTokens.has(token)) {
              console.log(`New token detected via WebSocket: ${token}`);
              monitorToken(token);
            }
          }
        }
      } catch (error) {
        console.error('Error processing WebSocket message:', error);
      }
    });
    
    ws.on('error', (error) => {
      console.error('WebSocket error:', error);
      // Reconnect after a delay
      setTimeout(() => {
        console.log('Reconnecting WebSocket...');
        setupWebSocketMonitoring();
      }, 5000);
    });
    
    ws.on('close', () => {
      console.log('WebSocket connection closed, reconnecting...');
      setTimeout(() => {
        setupWebSocketMonitoring();
      }, 5000);
    });
  }
  
  // Fallback polling method
  async function pollForNewTokens() {
    while (true) {
      try {
        const currentTokens = await getWalletTokens(WALLET_TO_TRACK.toString());
        
        // Check for new tokens
        for (const token of currentTokens) {
          if (!monitoringTokens.has(token)) {
            console.log(`New token detected via polling: ${token}`);
            monitorToken(token);
          }
        }
        
        // Wait before checking again
        await new Promise(resolve => setTimeout(resolve, 30000)); // Check every 30 seconds
      } catch (error) {
        console.error("Error polling for new tokens:", error);
        rotateConnection();
        await new Promise(resolve => setTimeout(resolve, 30000));
      }
    }
  }
  
  // Start the monitoring process
  watchForNewTokensRealtime();
}

async function getWalletTokens(walletAddress: string): Promise<string[]> {
  try {
    const pubKey = new PublicKey(walletAddress);
    const tokenAccounts = await getConnection().getParsedTokenAccountsByOwner(
      pubKey,
      { programId: TOKEN_PROGRAM_ID }
    );
    
    return tokenAccounts.value
      .filter(ta => {
        const amount = ta.account.data.parsed.info.tokenAmount.amount;
        return amount > 0;
      })
      .map(ta => ta.account.data.parsed.info.mint);
  } catch (error) {
    console.error("Error fetching wallet tokens:", error);
    rotateConnection();
    return [];
  }
}

main().catch(console.error);
EOF

# Make the setup script