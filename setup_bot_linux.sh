# Stop the current bot
pm2 stop solana-meme-bot
pm2 delete solana-meme-bot

# Update the bot code to reduce error logging
cat > solana_meme_bot.ts << 'EOF'
import { Connection, PublicKey, Keypair, ComputeBudgetProgram, VersionedTransaction } from '@solana/web3.js';
import { TOKEN_PROGRAM_ID } from '@solana/spl-token';
import bs58 from 'bs58';
import axios from 'axios';

// Configuration
const WATCH_THRESHOLD_MCAP = 45000; // $45k market cap - BUY
const SELL_THRESHOLD_MCAP = 60000;  // $60k market cap - SELL
const SOL_MINT = 'So11111111111111111111111111111111111111112';
const USDC_MINT = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';
const BUY_AMOUNT_SOL = 0.0001; // 0.0001 SOL per buy

// Multiple fast RPC endpoints
const RPC_ENDPOINTS = [
  'https://api.mainnet-beta.solana.com',
  'https://solana-api.projectserum.com'
];

// Your wallet configuration
const WALLET_TO_TRACK = new PublicKey('5qWya6UjwWnGVhdSBL3hyZ7B45jbk6Byt1hwd7ohEGXE');
const MY_WALLET = new PublicKey('6An8ZM33GPvuZFAs1BP9z5L5pczhPP8YdefDtaLLmUE');
const PRIVATE_KEY = bs58.decode("3WnGTqz9jbYiZyHBpoUPU8mpwwgdWKEB4UW4YvAxAvmWUDpuL1BgYGwf49TCEaezmquP3ah8XWohXZJyQSgUWWeN");

const PRIORITY_FEE_MICROLAMPORTS = 1_000_000; // 0.001 SOL

let connections: Connection[] = [];
let activeConnectionIndex = 0;

function getConnection(): Connection {
  return connections[activeConnectionIndex];
}

function rotateConnection() {
  activeConnectionIndex = (activeConnectionIndex + 1) % connections.length;
  console.log(`🔄 Switched to RPC endpoint: ${RPC_ENDPOINTS[activeConnectionIndex]}`);
  return getConnection();
}

async function main() {
  connections = RPC_ENDPOINTS.map(endpoint => 
    new Connection(endpoint, {
      commitment: 'confirmed',
      confirmTransactionInitialTimeout: 60000,
      disableRetryOnRateLimit: false,
    })
  );
  
  const wallet = Keypair.fromSecretKey(PRIVATE_KEY);
  console.log(`🚀 Bot started!`);
  console.log(`💰 Using wallet: ${wallet.publicKey.toString()}`);
  console.log(`👀 Tracking wallet: ${WALLET_TO_TRACK.toString()}`);
  console.log(`📈 Buy at: $${WATCH_THRESHOLD_MCAP.toLocaleString()} market cap`);
  console.log(`📉 Sell at: $${SELL_THRESHOLD_MCAP.toLocaleString()} market cap`);
  console.log(`💵 Buy amount: ${BUY_AMOUNT_SOL} SOL per token`);
  console.log(`⏰ Checking every 10 seconds...`);
  
  const monitoringTokens = new Set<string>();
  const boughtTokens = new Set<string>();
  const soldTokens = new Set<string>();
  
  async function monitorToken(tokenAddress: string) {
    if (monitoringTokens.has(tokenAddress)) return;
    monitoringTokens.add(tokenAddress);
    
    const tokenPublicKey = new PublicKey(tokenAddress);
    console.log(`🔍 Started monitoring token: ${tokenAddress}`);
    
    while (true) {
      try {
        const marketCap = await calculateMarketCap(tokenPublicKey);
        
        if (marketCap <= 0) {
          // Token has no liquidity, check less frequently
          await new Promise(resolve => setTimeout(resolve, 30000));
          continue;
        }
        
        console.log(`[${new Date().toISOString()}] ${tokenAddress.slice(0,8)}... - Market cap: $${marketCap.toFixed(2)}`);
        
        // BUY logic
        if (marketCap >= WATCH_THRESHOLD_MCAP && marketCap < SELL_THRESHOLD_MCAP && !boughtTokens.has(tokenAddress)) {
          console.log(`🎯 Token reached buy threshold of $${WATCH_THRESHOLD_MCAP.toLocaleString()}`);
          const success = await buyToken(tokenPublicKey, wallet);
          if (success) {
            boughtTokens.add(tokenAddress);
          }
        } 
        // SELL logic
        else if (marketCap >= SELL_THRESHOLD_MCAP && boughtTokens.has(tokenAddress) && !soldTokens.has(tokenAddress)) {
          console.log(`💰 Token reached sell threshold of $${SELL_THRESHOLD_MCAP.toLocaleString()}`);
          const success = await sellToken(tokenPublicKey, wallet);
          if (success) {
            soldTokens.add(tokenAddress);
            break;
          }
        }
        
        await new Promise(resolve => setTimeout(resolve, 10000)); // 10 seconds
      } catch (error) {
        console.error(`❌ Error monitoring token ${tokenAddress.slice(0,8)}...`);
        await new Promise(resolve => setTimeout(resolve, 30000));
      }
    }
  }
  
  async function calculateMarketCap(tokenPublicKey: PublicKey): Promise<number> {
    try {
      const tokenSupply = await getConnection().getTokenSupply(tokenPublicKey);
      const totalSupply = Number(tokenSupply.value.amount) / Math.pow(10, tokenSupply.value.decimals);
      const tokenPrice = await getTokenPriceInUSDC(tokenPublicKey);
      return tokenPrice * totalSupply;
    } catch (error) {
      rotateConnection();
      return 0;
    }
  }
  
  async function getTokenPriceInUSDC(tokenPublicKey: PublicKey): Promise<number> {
    try {
      // Get quote from Jupiter API
      const response = await axios.get(`https://quote-api.jup.ag/v6/quote`, {
        params: {
          inputMint: tokenPublicKey.toString(),
          outputMint: USDC_MINT,
          amount: 1000000, // 1 token with 6 decimals
          slippageBps: 100, // 1% slippage
        },
        timeout: 5000
      });
      
      if (response.data && response.data.outAmount) {
        return Number(response.data.outAmount) / 1_000_000; // Convert to USDC
      }
      return 0;
    } catch (error) {
      // Silently return 0 for tokens without liquidity
      return 0;
    }
  }
  
  async function buyToken(tokenPublicKey: PublicKey, wallet: Keypair): Promise<boolean> {
    try {
      console.log(`🛒 Buying token ${tokenPublicKey.toString()}`);
      
      const inputAmount = Math.floor(BUY_AMOUNT_SOL * 1_000_000_000); // Convert SOL to lamports
      
      // Get quote from Jupiter
      const quoteResponse = await axios.get(`https://quote-api.jup.ag/v6/quote`, {
        params: {
          inputMint: SOL_MINT,
          outputMint: tokenPublicKey.toString(),
          amount: inputAmount,
          slippageBps: 250, // 2.5% slippage
        },
        timeout: 10000
      });
      
      if (!quoteResponse.data) {
        console.error("❌ No quote found for buy");
        return false;
      }
      
      console.log(`💵 Expected input: ${BUY_AMOUNT_SOL} SOL`);
      console.log(`🪙 Expected output: ${Number(quoteResponse.data.outAmount) / Math.pow(10, 6)} tokens`);
      
      // Get swap transaction
      const swapResponse = await axios.post('https://quote-api.jup.ag/v6/swap', {
        quoteResponse: quoteResponse.data,
        userPublicKey: wallet.publicKey.toString(),
        wrapAndUnwrapSol: true,
        computeUnitPriceMicroLamports: PRIORITY_FEE_MICROLAMPORTS,
      }, {
        timeout: 10000
      });
      
      if (!swapResponse.data || !swapResponse.data.swapTransaction) {
        console.error("❌ Failed to get swap transaction");
        return false;
      }
      
      // Deserialize and sign transaction
      const swapTransactionBuf = Buffer.from(swapResponse.data.swapTransaction, 'base64');
      const transaction = VersionedTransaction.deserialize(swapTransactionBuf);
      transaction.sign([wallet]);
      
      // Send transaction
      const signature = await getConnection().sendTransaction(transaction, {
        skipPreflight: false,
        maxRetries: 3,
      });
      
      // Confirm transaction
      const confirmation = await getConnection().confirmTransaction(signature, 'confirmed');
      
      if (confirmation.value.err) {
        console.error("❌ Buy transaction failed:", confirmation.value.err);
        return false;
      }
      
      console.log(`✅ Buy successful! Txn: https://solscan.io/tx/${signature}`);
      return true;
    } catch (error) {
      console.error("❌ Error buying token");
      return false;
    }
  }
  
  async function sellToken(tokenPublicKey: PublicKey, wallet: Keypair): Promise<boolean> {
    try {
      console.log(`💸 Selling token ${tokenPublicKey.toString()}`);
      
      const tokenAccounts = await getConnection().getParsedTokenAccountsByOwner(
        wallet.publicKey,
        { mint: tokenPublicKey }
      );
      
      if (tokenAccounts.value.length === 0) {
        console.error("❌ No token account found");
        return false;
      }
      
      const tokenAccount = tokenAccounts.value[0];
      const balance = tokenAccount.account.data.parsed.info.tokenAmount.amount;
      const decimals = tokenAccount.account.data.parsed.info.tokenAmount.decimals;
      
      console.log(`🪙 Found ${Number(balance) / Math.pow(10, decimals)} tokens to sell`);
      
      // Get quote from Jupiter
      const quoteResponse = await axios.get(`https://quote-api.jup.ag/v6/quote`, {
        params: {
          inputMint: tokenPublicKey.toString(),
          outputMint: SOL_MINT,
          amount: balance,
          slippageBps: 250, // 2.5% slippage
        },
        timeout: 10000
      });
      
      if (!quoteResponse.data) {
        console.error("❌ No quote found for sell");
        return false;
      }
      
      console.log(`💰 Expected output: ${Number(quoteResponse.data.outAmount) / 1_000_000_000} SOL`);
      
      // Get swap transaction
      const swapResponse = await axios.post('https://quote-api.jup.ag/v6/swap', {
        quoteResponse: quoteResponse.data,
        userPublicKey: wallet.publicKey.toString(),
        wrapAndUnwrapSol: true,
        computeUnitPriceMicroLamports: PRIORITY_FEE_MICROLAMPORTS,
      }, {
        timeout: 10000
      });
      
      if (!swapResponse.data || !swapResponse.data.swapTransaction) {
        console.error("❌ Failed to get swap transaction");
        return false;
      }
      
      // Deserialize and sign transaction
      const swapTransactionBuf = Buffer.from(swapResponse.data.swapTransaction, 'base64');
      const transaction = VersionedTransaction.deserialize(swapTransactionBuf);
      transaction.sign([wallet]);
      
      // Send transaction
      const signature = await getConnection().sendTransaction(transaction, {
        skipPreflight: false,
        maxRetries: 3,
      });
      
      // Confirm transaction
      const confirmation = await getConnection().confirmTransaction(signature, 'confirmed');
      
      if (confirmation.value.err) {
        console.error("❌ Sell transaction failed:", confirmation.value.err);
        return false;
      }
      
      console.log(`✅ Sell successful! Txn: https://solscan.io/tx/${signature}`);
      return true;
    } catch (error) {
      console.error("❌ Error selling token");
      return false;
    }
  }
  
  async function watchForNewTokens() {
    const initialTokens = await getWalletTokens(WALLET_TO_TRACK.toString());
    console.log(`📊 Initial scan: Found ${initialTokens.length} tokens in tracked wallet`);
    
    for (const token of initialTokens) {
      monitorToken(token);
    }
    
    while (true) {
      try {
        const currentTokens = await getWalletTokens(WALLET_TO_TRACK.toString());
        
        for (const token of currentTokens) {
          if (!monitoringTokens.has(token)) {
            console.log(`🆕 New token detected: ${token}`);
            monitorToken(token);
          }
        }
        
        await new Promise(resolve => setTimeout(resolve, 60000)); // Check every minute
      } catch (error) {
        console.error("❌ Error checking for new tokens");
        rotateConnection();
        await new Promise(resolve => setTimeout(resolve, 60000));
      }
    }
  }
  
  watchForNewTokens();
}

async function getWalletTokens(walletAddress: string): Promise<string[]> {
  try {
    const pubKey = new PublicKey(walletAddress);
    const tokenAccounts = await getConnection().getParsedTokenAccountsByOwner(
      pubKey,
      { programId: TOKEN_PROGRAM_ID }
    );
    
    return tokenAccounts.value
      .filter(ta => {
        const amount = ta.account.data.parsed.info.tokenAmount.amount;
        return amount > 0;
      })
      .map(ta => ta.account.data.parsed.info.mint);
  } catch (error) {
    console.error("❌ Error fetching wallet tokens");
    rotateConnection();
    return [];
  }
}

main().catch(console.error);
EOF

# Start the cleaned up bot
pm2 start "npx tsx solana_meme_bot.ts" --name "solana-meme-bot"

# View logs
pm2 logs solana-meme-bot

